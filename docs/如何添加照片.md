# 如何添加照片到网站

## 📁 文件夹结构

```
training_web/
├── public/
│   └── images/          ← 在这里放置您的照片
│       ├── hero-image.jpg
│       ├── team-photo.png
│       └── service-1.jpg
├── src/
│   └── pages/
│       └── Home.jsx     ← 在这里修改图片配置
```

## 🖼️ 添加主页Hero区域照片

### 步骤1：准备照片
1. **选择合适的照片**
   - 格式：JPG, PNG, WebP
   - 尺寸：建议 1200x800 像素或更高
   - 文件大小：建议小于 1MB
   - 内容：护理服务、医疗设备、团队照片等

### 步骤2：放置照片文件
1. 将照片文件复制到 `public/images/` 文件夹
2. 给照片起一个有意义的名字，例如：
   - `hero-image.jpg` - 主页展示图
   - `healthcare-team.jpg` - 团队照片
   - `nursing-care.png` - 护理服务图

### 步骤3：修改配置
在 `src/pages/Home.jsx` 文件中找到这段代码：

```javascript
// 图片配置 - 方便快速更换
const HERO_IMAGE = {
  src: "/images/hero-image.jpg", // 修改为您的图片文件名
  alt: "Allcare Health Care 专业护理服务展示",
  fallback: "https://images.unsplash.com/photo-**********-5c350d0d3c56..."
};
```

将 `src` 字段改为您的图片文件名：
```javascript
src: "/images/您的图片文件名.jpg"
```

## 🌐 使用在线图片

如果您想使用在线图片（如公司网站上的图片），可以直接使用图片URL：

```javascript
const HERO_IMAGE = {
  src: "https://allcaremd.com/images/your-photo.jpg",
  alt: "图片描述",
  fallback: "/images/backup-image.jpg"
};
```

## 📋 完整示例

### 示例1：使用本地团队照片
```javascript
const HERO_IMAGE = {
  src: "/images/allcare-team-2024.jpg",
  alt: "Allcare Health Care 专业护理团队",
  fallback: "https://images.unsplash.com/photo-**********-5c350d0d3c56..."
};
```

### 示例2：使用护理服务照片
```javascript
const HERO_IMAGE = {
  src: "/images/nursing-service.png",
  alt: "专业居家护理服务展示",
  fallback: "/images/default-hero.jpg"
};
```

## 🔧 通过命令行添加照片

如果您有照片在桌面或其他位置，可以使用命令行复制：

```bash
# 复制桌面上的照片到项目
cp ~/Desktop/your-photo.jpg public/images/hero-image.jpg

# 或者从下载文件夹复制
cp ~/Downloads/team-photo.png public/images/team-photo.png
```

## ✅ 验证照片是否添加成功

1. 启动开发服务器：`npm start`
2. 打开浏览器访问 http://localhost:3000
3. 查看主页Hero区域是否显示了您的照片
4. 如果照片没有显示，检查：
   - 文件路径是否正确
   - 文件名是否匹配
   - 图片格式是否支持

## 🎨 图片优化建议

### 推荐尺寸
- **主页Hero图片**：1200x800 像素
- **服务展示图片**：800x600 像素
- **团队照片**：1000x667 像素

### 文件大小
- 主要展示图片：< 500KB
- 背景图片：< 1MB
- 图标类图片：< 100KB

### 图片内容建议
- 护理人员与客户互动
- 专业的医疗设备
- 温馨的居家护理场景
- 团队合影
- 公司办公环境

## 🚨 常见问题

**Q: 图片不显示怎么办？**
A: 检查文件路径、文件名大小写、图片格式

**Q: 图片显示模糊怎么办？**
A: 使用更高分辨率的图片，建议至少1200像素宽

**Q: 可以使用什么格式的图片？**
A: JPG, PNG, WebP, SVG都支持，推荐JPG或PNG

**Q: 图片文件太大怎么办？**
A: 使用在线压缩工具如 TinyPNG 或 Squoosh 压缩图片
