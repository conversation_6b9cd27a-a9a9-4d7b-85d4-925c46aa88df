# 图片更换指南

## 主页Hero区域图片更换

### 快速更换方法

在 `src/pages/Home.jsx` 文件的顶部，找到以下配置：

```javascript
// 图片配置 - 方便快速更换
const HERO_IMAGE = {
  src: "https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
  alt: "Allcare Health Care 专业护理服务展示",
  fallback: "/api/placeholder/600/400"
};
```

### 更换步骤

1. **更换在线图片**：
   - 将 `src` 字段替换为新的图片URL
   - 建议使用高质量图片（推荐尺寸：1000x600像素或更高）

2. **更换本地图片**：
   - 将图片文件放入 `public/images/` 文件夹
   - 将 `src` 字段改为 `/images/你的图片文件名.jpg`

3. **更新描述**：
   - 修改 `alt` 字段为适合的图片描述

### 示例

```javascript
// 使用本地图片
const HERO_IMAGE = {
  src: "/images/healthcare-team.jpg",
  alt: "Allcare专业护理团队",
  fallback: "/api/placeholder/600/400"
};

// 使用在线图片
const HERO_IMAGE = {
  src: "https://example.com/your-image.jpg",
  alt: "护理服务展示",
  fallback: "/api/placeholder/600/400"
};
```

### 图片要求

- **格式**：JPG, PNG, WebP
- **尺寸**：建议 1000x600 像素或更高
- **文件大小**：建议小于 500KB
- **内容**：与护理服务相关的专业图片

### 注意事项

- 图片会自动适应容器大小
- 包含悬停动画效果
- 自动错误处理（如果主图片加载失败，会显示备用图片）
- 响应式设计，在移动设备上自动调整

### 推荐图片来源

1. **Unsplash**：https://unsplash.com/s/photos/healthcare
2. **Pexels**：https://www.pexels.com/search/healthcare/
3. **公司自有图片**：放置在 `public/images/` 文件夹中

### 技术细节

图片使用了以下特性：
- `object-fit: cover` - 保持比例填充容器
- `border-radius: 3` - 圆角边框
- 阴影效果和悬停动画
- 错误处理机制
